import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getTextByCode } from '/@/components/Form/src/utils/areaDataUtil';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    width: 160,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '草稿', color: 'default' },
        1: { text: '备案', color: 'success' },
        2: { text: '撤回', color: 'warning' },
        4: { text: '作废', color: 'error' },
      };
      const status = statusMap[text];
      return status ? render.renderTag(status.text, status.color) : '';
    },
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 160,
    customRender: ({ _text }) => {
      return '厦门市城市建设发展投资有限公司';
    },
  },
  {
    title: '所属企业',
    dataIndex: 'companyName',
    width: 160,
    customRender: ({ text }) => {
      const companyMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司',
      };
      return companyMap[text] || '-';
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 160,
    customRender: ({ text }) => {
      const unitMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司',
      };
      return unitMap[text] || '-';
    },
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '区县',
    dataIndex: 'area',
    width: 100,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return text === 1 ? '是' : '否';
    },
  },
  {
    title: '广告位面积(㎡)',
    dataIndex: 'adArea',
    width: 140,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 150,
    customRender: ({ text }) => {
      if (!text || !Array.isArray(text)) return '-';
      const statusMap = {
        0: '闲置',
        1: '自用',
        2: '出租',
        3: '出借',
        4: '占用',
        5: '欠租',
        6: '转让',
        7: '其他',
      };
      return text.map((v) => statusMap[v]).join('、');
    },
  },
  {
    title: '设置时间',
    dataIndex: 'setDate',
    width: 120,
  },
  {
    title: '资产原值(元)',
    dataIndex: 'assetsAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值(元)',
    dataIndex: 'bookAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 150,
  },
  {
    title: '剩余可使用年限',
    dataIndex: 'remainingUsefulYears',
    width: 150,
  },
  {
    title: '广告位数量',
    dataIndex: 'adQuantity',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(0) : '';
    },
  },
  {
    title: '是否审批',
    dataIndex: 'approvalStatus',
    width: 100,
    customRender: ({ text }) => {
      return text === 0 ? '是' : '否';
    },
  },
  {
    title: '是否入账',
    dataIndex: 'entryStatus',
    width: 100,
    customRender: ({ text }) => {
      return text === 0 ? '是' : '否';
    },
  },
  {
    title: '广告位管理部门',
    dataIndex: 'adOrg',
    width: 160,
  },
  {
    title: '广告位联系人',
    dataIndex: 'adContacts',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'contactsPhone',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'code',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'companyName',
    label: '所属企业',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'enterpriseCode',
    label: '企业自定义编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入企业自定义编号',
    },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'assetsLocation',
    label: '资产位置',
    component: 'JAreaLinkage',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择省/市/区',
    },
  },
  {
    field: 'address',
    label: '详细地址',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入详细地址',
    },
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'assetsStatus',
    label: '资产使用状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择资产使用状态',
      mode: 'multiple',
      dictCode: 'land_assets_status',
    },
  },
  {
    field: 'adArea',
    label: '广告位面积',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小值',
      precision: 2,
      min: 0,
    },
  },
  // {
  //   field: 'adAreaMax',
  //   label: '广告位面积最大值',
  //   component: 'InputNumber',
  //   colProps: { span: 6 },
  //   componentProps: {
  //     placeholder: '最大值',
  //     precision: 2,
  //     min: 0,
  //   },
  // },
  {
    field: 'approvalStatus',
    label: '是否审批',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否审批',
      ictCode: 'yes_no',
    },
  },
  {
    field: 'entryStatus',
    label: '是否入账',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否入账',
      ictCode: 'yes_no',
    },
  },
];
