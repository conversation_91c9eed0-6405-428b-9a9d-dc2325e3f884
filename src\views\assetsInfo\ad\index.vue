<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:import-outlined" @click="handleImport">导入</a-button> -->
        <import-modal @success="importSuccess" exportTemplateName="下载导入模板" :exportTemplateUrl="downloadTemplate" :importUrl="importExcel" />
        <a-button
          type="primary"
          preIcon="ant-design:export-outlined"
          @click="handleExport"
          :loading="exportLoading"
          :disabled="selectedRowKeys.length === 0"
        >
          导出
        </a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" :loading="exportLoading" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 导入模态框 -->
    <ImportModal @register="registerImportModal" @success="handleImportSuccess" />
  </div>
</template>

<script lang="ts" name="ad-list" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema } from './ad.data';
  import { list, deleteAd, exportExcel, importExcel, downloadTemplate } from './ad.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  // import ImportModal from './ImportModal.vue';
  import ImportModal from '/@/components/ImportModal/index.vue';
  import { useMethods } from '/@/hooks/system/useMethods';
  const { createMessage } = useMessage();

  const router = useRouter();
  const exportLoading = ref(false);
  const { handleExportXls } = useMethods();

  // 导入模态框
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'ad-list',
    tableProps: {
      title: '广告位信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'ad_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [['createTime', ['createTimeMin', 'createTimeMax'], 'YYYY-MM-DD']],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        if (params.adArea) {
          const [min, max] = params.adArea.split(',');
          params.adAreaMin = min;
          params.adAreaMax = max;
        }
        if (params.assetsLocation) {
          console.log(params.assetsLocation, 'params.assetsLocation');
          const [province, city, area] = params.assetsLocation.split(',');
          params.province = province;
          params.city = city;
          params.area = area;
        }
        console.log(params, 'params');
        // getSumHandle(params);
        return params;
      },
      // 显示合计行
      showSummary: true,
      summaryFunc: (data) => {
        console.log('data', data);
        // 使用工具函数自动计算合计
        const totals = mapTableTotalSummary(data, [
          'adArea',
          'adQuantity',
          'assetsAmount',
          'bookAmount',
        ]);

        // 格式化数值
        Object.keys(totals).forEach((key) => {
          if (typeof totals[key] === 'number') {
            totals[key] = Number(totals[key].toFixed(2));
          }
        });

        return [totals];
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/assetsInfo/ad/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/assetsInfo/ad/edit/${record.id}`);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteAd({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm();
    formData.ids = selectedRowKeys.value.join();
    exportLoading.value = true;
    await handleExportXls('广告位资产列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    const formData = getForm();
    exportLoading.value = true;
    await handleExportXls('广告位资产列表', exportExcel, formData, 'post').then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  }

  /**
   * 导入事件
   */
  function handleImport() {
    openImportModal(true);
  }

  /**
   * 导入成功回调
   */
  function handleImportSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style> 