import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getTextByCode } from '/@/components/Form/src/utils/areaDataUtil';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    width: 160,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '草稿', color: 'default' },
        1: { text: '备案', color: 'success' },
        2: { text: '撤回', color: 'warning' },
        4: { text: '作废', color: 'error' },
      };
      const status = statusMap[text];
      return status ? render.renderTag(status.text, status.color) : '';
    },
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 160,
  },
  {
    title: '所属企业',
    dataIndex: 'companyName',
    width: 160,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 160,
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
    customRender: ({ record }) => {
      return getTextByCode(record.province);
    },
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
    customRender: ({ record }) => {
      return getTextByCode(record.city);
    },
  },
  {
    title: '区县',
    dataIndex: 'area',
    width: 100,
    customRender: ({ record }) => {
      return getTextByCode(record.area);
    },
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return text === 1 ? '是' : '否';
    },
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 150,
    customRender: ({ text }) => {
      const textArray = text ? text.split(',') : [];
      return h(
        'div',
        textArray.map((item) => {
          const finalText = render.renderDict(item, 'land_assets_status', true);
          return h('span', finalText);
        })
      );
    },
  },
  {
    title: '是否正常使用',
    dataIndex: 'normal',
    width: 120,
    customRender: ({ text }) => {
      return text === 0 ? '可正常使用' : text === 1 ? '无法正常使用' : '-';
    },
  },
  {
    title: '是否被无偿占用',
    dataIndex: 'freeOccupy',
    width: 120,
    customRender: ({ text }) => {
      return text === 0 ? '是' : text === 1 ? '否' : '-';
    },
  },
  {
    title: '入账日期',
    dataIndex: 'assetEntryDate',
    width: 120,
  },
  {
    title: '设备原值(元)',
    dataIndex: 'deviceAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '累计折旧(元)',
    dataIndex: 'totalDepreciation',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '设备折旧年限',
    dataIndex: 'deviceDepreciationYears',
    width: 150,
  },
  {
    title: '设备折旧剩余年限',
    dataIndex: 'remainingYearsOfEquipmentDepreciation',
    width: 150,
  },
  {
    title: '账面价值(元)',
    dataIndex: 'bookAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'companyName',
    label: '所属企业',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'code',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'enterpriseCode',
    label: '企业自定义编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入企业自定义编号',
    },
  },
  {
    field: 'assetsLocation',
    label: '资产位置',
    component: 'JAreaLinkage',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择省/市/区',
    },
  },
  {
    field: 'address',
    label: '详细地址',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入详细地址',
    },
  },
  {
    field: 'assetsStatus',
    label: '资产使用状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择资产使用状态',
      mode: 'multiple',
      dictCode: 'land_assets_status',
    },
  },
  {
    field: 'normal',
    label: '是否正常使用',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否正常使用',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'freeOccupy',
    label: '是否被无偿占用',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否被无偿占用',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'assetEntryDate',
    label: '入账日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'deviceAmount',
    label: '设备原值',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小值',
      precision: 2,
      min: 0,
    },
  },
  {
    field: 'bookAmount',
    label: '账面价值最小值',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小值',
      precision: 2,
      min: 0,
    },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
